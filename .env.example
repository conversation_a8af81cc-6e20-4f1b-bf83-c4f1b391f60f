# Flask应用配置
FLASK_ENV=production
SECRET_KEY=your-super-secret-key-change-this

# 腾讯云数据库配置
# 公网连接（推荐用于生产环境）
DB_HOST=gz-cynosdbmysql-grp-0p7t50v3.sql.tencentcdb.com
DB_PORT=23387
DB_USERNAME=srmyy_123
DB_PASSWORD=gg9gpaEqkg4s
DB_NAME=performance_calculator

# 内网连接（如果服务器在腾讯云内网）
# DB_HOST=*********
# DB_PORT=3306

# 完整数据库连接字符串（可选，会覆盖上面的单独配置）
DATABASE_URL=mysql+pymysql://srmyy_123:<EMAIL>:23387/performance_calculator

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 文件上传配置
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=52428800  # 50MB

# 邮件配置（可选）
MAIL_SERVER=smtp.qq.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# 腾讯云配置
TENCENTCLOUD_SECRET_ID=your-tencent-cloud-secret-id
TENCENTCLOUD_SECRET_KEY=your-tencent-cloud-secret-key
TENCENTCLOUD_REGION=ap-beijing

# COS存储配置
COS_BUCKET=your-cos-bucket-name
COS_REGION=ap-beijing

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5