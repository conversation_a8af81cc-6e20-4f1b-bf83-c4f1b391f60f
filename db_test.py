#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试腾讯云数据库配置
"""

print("🚀 快速测试腾讯云数据库配置")
print("=" * 50)

# 测试1: 检查PyMySQL
try:
    import pymysql
    print("✅ PyMySQL已安装，版本:", pymysql.__version__)
except ImportError:
    print("❌ PyMySQL未安装")
    exit(1)

# 测试2: 检查配置
print("\n📊 数据库配置信息:")
config = {
    'host': 'gz-cynosdbmysql-grp-0p7t50v3.sql.tencentcdb.com',
    'port': 23387,
    'user': 'srmyy_123',
    'password': 'gg9gpaEqkg4s',
    'database': 'performance_calculator'
}

for key, value in config.items():
    if key == 'password':
        print(f"   {key}: {'*' * len(value)}")
    else:
        print(f"   {key}: {value}")

# 测试3: 尝试连接（短超时）
print("\n🔄 尝试连接数据库（5秒超时）...")
try:
    connection = pymysql.connect(
        host=config['host'],
        port=config['port'],
        user=config['user'],
        password=config['password'],
        database=config['database'],
        charset='utf8mb4',
        connect_timeout=5
    )
    
    print("✅ 数据库连接成功！")
    
    # 执行简单查询
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        print(f"✅ 查询测试成功: {result}")
    
    connection.close()
    print("✅ 连接已关闭")
    
except Exception as e:
    print(f"❌ 连接失败: {str(e)}")
    print("\n可能的原因:")
    print("   1. 网络连接问题")
    print("   2. 数据库服务器不可达")
    print("   3. 认证信息错误")

print("\n" + "=" * 50)
print("🎯 数据库配置已更新完成！")
print("\n📝 配置文件已更新:")
print("   - app.py: 添加了数据库连接逻辑")
print("   - web_config.py: 更新了生产环境配置")
print("   - .env: 配置了腾讯云数据库信息")
print("   - .env.example: 更新了配置模板")

print("\n🛠️ 新增工具:")
print("   - init_tencent_db.py: 数据库初始化脚本")
print("   - test_db_connection.py: 完整的连接测试工具")
print("   - 腾讯云数据库配置说明.md: 详细配置文档")

print("\n🚀 下一步操作:")
print("   1. 如果连接成功，运行: python init_tencent_db.py")
print("   2. 然后启动应用: python app.py")
print("   3. 访问 http://localhost:5000 使用系统")
