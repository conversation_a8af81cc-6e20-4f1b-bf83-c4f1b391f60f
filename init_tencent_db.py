#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯云数据库初始化脚本
用于初始化腾讯云MySQL数据库的表结构和默认数据
"""

import os
import sys
from dotenv import load_dotenv
from werkzeug.security import generate_password_hash

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def init_database():
    """初始化腾讯云数据库"""
    try:
        # 导入应用和数据库
        from app import app, db, User, DoctorGroup
        
        print("🔄 正在连接腾讯云数据库...")
        
        with app.app_context():
            # 检查数据库连接
            db_uri = app.config['SQLALCHEMY_DATABASE_URI']
            print(f"📊 数据库连接: {db_uri.replace(os.environ.get('DB_PASSWORD', ''), '***')}")
            
            # 创建所有表
            print("🔄 正在创建数据库表...")
            db.create_all()
            print("✅ 数据库表创建成功")
            
            # 检查是否已存在管理员用户
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                # 创建默认管理员用户
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    role='admin',
                    doctor_name='系统管理员'
                )
                db.session.add(admin)
                print("✅ 创建默认管理员用户: admin / admin123")
            else:
                print("ℹ️  管理员用户已存在")
            
            # 检查是否已存在默认医生分组
            existing_groups = DoctorGroup.query.count()
            if existing_groups == 0:
                # 创建默认医生分组
                default_groups = {
                    "赖红琳组": ["赖红琳", "李凡", "陈小永"],
                    "吴西雅组": ["廖丽军", "吴西雅", "吴海凤"],
                    "童波组": ["童波", "刘娜", "唐斌"],
                    "夏顺生组": ["梁莹", "夏顺生", "陈卫群"],
                    "邹国明组": ["邹国明", "周洪"],
                    "其他组": ["郭玲玲", "李星", "张琦", "黄颖", "欧阳国泉", "郭猷殚"]
                }
                
                for group_name, doctors in default_groups.items():
                    group = DoctorGroup(
                        group_name=group_name,
                        doctors=str(doctors),  # 转换为字符串存储
                        created_by=admin.id if admin else None
                    )
                    db.session.add(group)
                
                print("✅ 创建默认医生分组")
            else:
                print("ℹ️  医生分组已存在")
            
            # 提交所有更改
            db.session.commit()
            print("✅ 数据库初始化完成！")
            
            # 显示连接信息
            print("\n📋 数据库信息:")
            print(f"   主机: {os.environ.get('DB_HOST', '未配置')}")
            print(f"   端口: {os.environ.get('DB_PORT', '未配置')}")
            print(f"   用户: {os.environ.get('DB_USERNAME', '未配置')}")
            print(f"   数据库: {os.environ.get('DB_NAME', 'performance_calculator')}")
            
            return True
            
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        return False

def test_connection():
    """测试数据库连接"""
    try:
        from app import app, db
        
        with app.app_context():
            # 尝试执行简单查询
            db.engine.execute('SELECT 1')
            print("✅ 数据库连接测试成功")
            return True
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 腾讯云数据库初始化工具")
    print("=" * 50)
    
    # 检查环境变量
    required_vars = ['DB_HOST', 'DB_USERNAME', 'DB_PASSWORD']
    missing_vars = [var for var in required_vars if not os.environ.get(var)]
    
    if missing_vars:
        print("❌ 缺少必要的环境变量:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n请在 .env 文件中配置这些变量，或者设置环境变量")
        return False
    
    # 测试连接
    print("🔄 测试数据库连接...")
    if not test_connection():
        return False
    
    # 初始化数据库
    print("\n🔄 开始初始化数据库...")
    if init_database():
        print("\n🎉 腾讯云数据库初始化成功！")
        print("\n📝 下一步:")
        print("   1. 使用 admin / admin123 登录系统")
        print("   2. 在管理界面中配置医生分组")
        print("   3. 开始使用绩效计算功能")
        return True
    else:
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
