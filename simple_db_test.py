#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的腾讯云数据库连接测试
"""

import sys
import time

def test_connection():
    """测试数据库连接"""
    try:
        import pymysql
        
        # 腾讯云数据库配置
        config = {
            'host': 'gz-cynosdbmysql-grp-0p7t50v3.sql.tencentcdb.com',
            'port': 23387,
            'user': 'srmyy_123',
            'password': 'gg9gpaEqkg4s',
            'database': 'performance_calculator',
            'charset': 'utf8mb4',
            'connect_timeout': 10
        }
        
        print("🔄 正在连接腾讯云数据库...")
        print(f"📊 主机: {config['host']}")
        print(f"📊 端口: {config['port']}")
        print(f"📊 用户: {config['user']}")
        print(f"📊 数据库: {config['database']}")
        
        # 测试连接
        start_time = time.time()
        connection = pymysql.connect(**config)
        
        # 执行测试查询
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            
            cursor.execute("SELECT DATABASE()")
            db_name = cursor.fetchone()
            
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
        
        connection.close()
        end_time = time.time()
        
        print(f"✅ 数据库连接成功！")
        print(f"   MySQL版本: {version[0]}")
        print(f"   当前数据库: {db_name[0]}")
        print(f"   现有表数量: {len(tables)}")
        if tables:
            print(f"   表列表: {', '.join([table[0] for table in tables])}")
        print(f"   连接耗时: {end_time - start_time:.2f}秒")
        
        return True
        
    except ImportError:
        print("❌ PyMySQL模块未安装，请运行: pip install PyMySQL")
        return False
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

def test_internal_connection():
    """测试内网连接"""
    try:
        import pymysql
        
        # 腾讯云内网数据库配置
        config = {
            'host': '*********',
            'port': 3306,
            'user': 'srmyy_123',
            'password': 'gg9gpaEqkg4s',
            'database': 'performance_calculator',
            'charset': 'utf8mb4',
            'connect_timeout': 5
        }
        
        print("\n🔄 正在测试内网连接...")
        print(f"📊 内网主机: {config['host']}")
        print(f"📊 内网端口: {config['port']}")
        
        # 测试连接
        start_time = time.time()
        connection = pymysql.connect(**config)
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
        
        connection.close()
        end_time = time.time()
        
        print(f"✅ 内网连接成功！")
        print(f"   连接耗时: {end_time - start_time:.2f}秒")
        print("   💡 建议：如果服务器在腾讯云内网，使用内网连接更快更稳定")
        
        return True
        
    except Exception as e:
        print(f"⚠️  内网连接失败: {str(e)}")
        print("   💡 这是正常的，如果不在腾讯云内网环境中")
        return False

def main():
    """主函数"""
    print("🚀 腾讯云数据库连接测试")
    print("=" * 50)
    
    # 测试公网连接
    success = test_connection()
    
    # 测试内网连接（可能失败，这是正常的）
    test_internal_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 数据库配置成功！可以正常使用腾讯云数据库。")
        print("\n📝 下一步:")
        print("   1. 运行 python init_tencent_db.py 初始化数据库")
        print("   2. 运行 python app.py 启动Web应用")
    else:
        print("❌ 数据库连接失败，请检查:")
        print("   1. 网络连接是否正常")
        print("   2. 数据库配置是否正确")
        print("   3. PyMySQL是否已安装")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
