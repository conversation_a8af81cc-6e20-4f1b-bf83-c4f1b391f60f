# 腾讯云数据库配置说明

## 📋 数据库信息

本项目已配置使用腾讯云MySQL数据库，具体信息如下：

### 连接信息
- **数据库类型**: MySQL 8.0
- **用户名**: `srmyy_123`
- **密码**: `gg9gpaEqkg4s`
- **数据库名**: `performance_calculator`

### 连接地址
- **公网地址**: `gz-cynosdbmysql-grp-0p7t50v3.sql.tencentcdb.com:23387`
- **内网地址**: `*********:3306`

## 🔧 配置方式

### 1. 环境变量配置（推荐）

在 `.env` 文件中配置：

```bash
# 腾讯云数据库配置
DB_HOST=gz-cynosdbmysql-grp-0p7t50v3.sql.tencentcdb.com
DB_PORT=23387
DB_USERNAME=srmyy_123
DB_PASSWORD=gg9gpaEqkg4s
DB_NAME=performance_calculator
```

### 2. 内网连接配置

如果服务器部署在腾讯云内网，可以使用内网地址（更快更稳定）：

```bash
# 内网连接配置
DB_HOST=*********
DB_PORT=3306
DB_USERNAME=srmyy_123
DB_PASSWORD=gg9gpaEqkg4s
DB_NAME=performance_calculator
```

## 🚀 快速开始

### 1. 测试数据库连接

```bash
# 测试数据库连接
python test_db_connection.py
```

### 2. 初始化数据库

```bash
# 初始化数据库表和默认数据
python init_tencent_db.py
```

### 3. 启动应用

```bash
# 启动Web应用
python app.py
```

## 📊 数据库表结构

### User 表（用户表）
- `id`: 主键
- `username`: 用户名
- `email`: 邮箱
- `password_hash`: 密码哈希
- `role`: 角色（doctor/admin）
- `doctor_name`: 关联的医生姓名
- `created_at`: 创建时间
- `is_active`: 是否激活

### CalculationTask 表（计算任务表）
- `id`: 主键
- `user_id`: 用户ID（外键）
- `task_name`: 任务名称
- `status`: 状态（pending/processing/completed/failed）
- `created_at`: 创建时间
- `completed_at`: 完成时间
- `result_file`: 结果文件路径
- `error_message`: 错误信息
- `data_folder`: 数据文件夹路径
- `duplicate_strategy`: 重复处理策略

### DoctorGroup 表（医生分组表）
- `id`: 主键
- `group_name`: 分组名称
- `doctors`: 医生列表（JSON格式）
- `created_by`: 创建者ID（外键）
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 🔒 安全注意事项

1. **密码保护**: 数据库密码已配置在环境变量中，不会出现在代码中
2. **连接加密**: 使用SSL连接确保数据传输安全
3. **访问控制**: 数据库配置了用户权限，只能访问指定的数据库
4. **备份策略**: 腾讯云提供自动备份功能

## 🛠️ 故障排除

### 连接超时
如果遇到连接超时，可能的原因：
1. 网络问题：检查网络连接
2. 防火墙：确保端口23387（公网）或3306（内网）开放
3. 数据库状态：确认数据库实例正在运行

### 权限错误
如果遇到权限错误：
1. 检查用户名和密码是否正确
2. 确认用户有访问指定数据库的权限
3. 检查数据库名称是否正确

### 字符编码问题
确保使用UTF-8编码：
```python
# 在连接字符串中指定字符集
mysql+pymysql://user:pass@host:port/db?charset=utf8mb4
```

## 📞 技术支持

如果遇到数据库相关问题，可以：
1. 查看应用日志：`logs/` 目录下的日志文件
2. 运行连接测试：`python test_db_connection.py`
3. 检查环境变量配置：确认 `.env` 文件中的配置正确

## 🔄 数据迁移

如果需要从其他数据库迁移数据：
1. 导出原数据库数据
2. 使用 `init_tencent_db.py` 初始化新数据库
3. 导入数据到腾讯云数据库
4. 验证数据完整性
